'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useSessionAvailability, isSessionAvailable, getAvailabilityText } from '@/hooks/useSessionAvailability';
import { useFormSecurity } from '@/hooks/useFormSecurity';
import { HoneypotField, TimestampField, SecurityTokenField } from '@/components/ui/honeypot-field';
import { ScrollTriggerMotion } from '@/components/motion/MotionWrapper';
import { titleScrollVariants, contentScrollVariants } from '@/lib/motion-config';
import { detectBrowser } from '@/utils/browserDetection';
import {
  Zap,
  Clock,
  Calendar,
  Info,
  MapPin,
  CircleDollarSign,
  CheckCircle,
  ArrowRight,
  Cog
} from 'lucide-react';

// 表單選項配置
const watchTypeOptions = [
  '機械錶',
  '石英錶',
  '電子錶',
  '智慧錶',
  '無'
];

const genderOptions = [
  { value: 'male', label: '男' },
  { value: 'female', label: '女' },
  { value: 'other', label: '其他' }
];

const ageOptions = [
  { value: '<20', label: '<20' },
  { value: '20-29', label: '20-29' },
  { value: '30-39', label: '30-39' },
  { value: '40-49', label: '40-49' },
  { value: '50-59', label: '50-59' },
  { value: '>=60', label: '>=60' }
];

const regionOptions = [
  { value: 'north', label: '北部地區（基、北、桃、竹、苗）' },
  { value: 'central', label: '中部地區（中、彰、雲、投）' },
  { value: 'south', label: '南部地區（嘉、南、高、屏）' },
  { value: 'offshore', label: '外島地區' },
  { value: 'other', label: 'Other' }
];

const participationTypeOptions = [
  { value: 'individual', label: '個人報名' },
  { value: 'pair', label: '雙人團報' }
];

// 付款狀態常數（已移至 lib/constants.ts）
// const PAYMENT_STATUS = {
//   PAID: 1,        // 已付款
//   RESERVED: 2,    // 保留中（已報名未付款）
//   CANCELLED: 3    // 已取消
// } as const;

// 活動配置
const eventConfig = {
  name: '錶匠體驗機芯拆解',
  price: 1500,
  sessionOptions: [
    '台中 07/18（五）19:20',
    '台北 07/19（六）13:20',
    '台北 07/20（日）13:20',
    '台北 07/20（日）15:20',
    '台北 07/25（五）19:20',
    '台北 07/26（六）13:20',
    '台北 07/26（六）15:20',
    '有意願但無合適時間地點'
  ],
  // 每個場次的名額上限配置
  sessionCapacity: {
    '台中 07/18（五）19:20': 8,
    '台北 07/19（六）13:20': 8,
    '台北 07/20（日）13:20': 8,
    '台北 07/20（日）15:20': 8,
    '台北 07/25（五）19:20': 8,
    '台北 07/26（六）13:20': 8,
    '台北 07/26（六）15:20': 8,
    '有意願但無合適時間地點': 999 // 無限制
  }
};

// 活動介紹內容配置 - 非技術人員可以輕鬆修改此區塊
// 支援 1-4 個內容區塊，系統會自動調整佈局
const activitySections = [
  {
    id: 'power-transmission',
    title: '動力傳導',
    icon: 'Zap', // 使用 Lucide React icon
    points: [
      '感受金屬發條緊縮的張力，體驗動力如何在發條盒中被儲存與釋放。',
      '觀察齒輪、分針、時針如何透過精密齒輪的傳動運作，讓時間得以被精確計算，並透過錶盤呈現與傳達。',
      '最特別的是，您將有機會親手操作，拆卸與組裝發條盒及傳動輪系，透過放大鏡觀察其微妙的機械美學，以及齒輪精準咬合的奧妙。'
    ]
  },
  {
    id: 'escapement',
    title: '擺輪擒縱',
    icon: 'Cog', // 使用 Lucide React icon
    points: [
      '發條盒如何與擒縱器？為何動力傳遞需要擒縱機構的調節力？',
      '秒針、分針、時針如何運作？透過不同齒輪的傳動，時間才能被精確顯示？',
      '動力傳遞的秘密！如何透過齒輪比調整機構的運行速度？'
    ]
  }
  // 可以輕鬆添加更多區塊，例如：
  // {
  //   id: 'time-setting',
  //   title: '調時結構',
  //   icon: 'Gear',
  //   points: [
  //     '了解調時機構的運作原理...',
  //     '體驗精密的時間調整過程...',
  //     '探索錶冠與機芯的連接奧秘...'
  //   ]
  // },
  // {
  //   id: 'dial-hands',
  //   title: '面盤指針',
  //   icon: 'Settings',
  //   points: [
  //     '認識面盤的製作工藝...',
  //     '了解指針的設計與功能...',
  //     '體驗組裝面盤的精密過程...'
  //   ]
  // }
];

// Lucide React 圖標組件映射 - 統一使用金色
const IconComponents = {
  Zap: Zap,
  Clock: Clock,
  Calendar: Calendar,
  Info: Info,
  MapPin: MapPin,
  CircleDollarSign: CircleDollarSign,
  CheckCircle: CheckCircle,
  ArrowRight: ArrowRight,
  Cog: Cog
};

interface FormData {
  // 場次時間
  sessionTimes: string[];
  customTimeLocation: string;

  // 參加方式
  participationType: 'individual' | 'pair' | '';

  // 個人資訊
  name: string;
  email: string;
  phone: string;

  // 同行者資訊
  companionName: string;
  companionEmail: string;

  // 基本資訊
  gender: 'male' | 'female' | 'other' | '';
  age: '<20' | '20-29' | '30-39' | '40-49' | '50-59' | '>=60' | '';
  region: 'north' | 'central' | 'south' | 'offshore' | 'other' | '';

  // 手錶相關
  watchTypes: string[];
  watchBrands: string;

  // 其他
  questions: string;
  agreeToTerms: boolean;
}

export default function EventsPage() {
  // iOS Safari 檢測
  const [isIOSSafari, setIsIOSSafari] = useState(false);

  // 抓取 UTM 參數
  const [utmParams, setUtmParams] = useState({
    utm_campaign: '',
    utm_source_platform: '',
    utm_marketing_tactic: '',
    utm_creative_format: ''
  });

  const [formData, setFormData] = useState<FormData>({
    sessionTimes: [],
    customTimeLocation: '',
    participationType: '',
    name: '',
    email: '',
    phone: '',
    companionName: '',
    companionEmail: '',
    gender: '',
    age: '',
    region: '',
    watchTypes: [],
    watchBrands: '',
    questions: '',
    agreeToTerms: false,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);

  // 使用場次名額 Hook
  const { availability, loading: availabilityLoading, error: availabilityError } = useSessionAvailability();

  // 使用表單安全 Hook
  const {
    honeypotValue,
    formStartTime,
    securityToken,
    setHoneypotValue,
    validateBeforeSubmit,
    resetSecurity,
  } = useFormSecurity();

  // iOS Safari 檢測
  useEffect(() => {
    const browser = detectBrowser();
    setIsIOSSafari(browser.isIOSSafari);
    if (browser.isIOSSafari) {
      console.log('EventsPage: iOS Safari detected, applying fixes');
    }
  }, []);



  // 在頁面載入時抓取 UTM 參數
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const extractedUtmParams = {
        utm_campaign: urlParams.get('utm_campaign') || '',
        utm_source_platform: urlParams.get('utm_source_platform') || '',
        utm_marketing_tactic: urlParams.get('utm_marketing_tactic') || '',
        utm_creative_format: urlParams.get('utm_creative_format') || ''
      };

      setUtmParams(extractedUtmParams);

      // 在開發環境下顯示抓取到的 UTM 參數
      if (process.env.NODE_ENV === 'development') {
        console.log('🔗 抓取到的 UTM 參數:', extractedUtmParams);
      }
    }
  }, []);

  // 表單驗證函數
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // 場次時間驗證
    if (formData.sessionTimes.length === 0) {
      newErrors.sessionTimes = '請選擇至少一個場次時間';
    }

    // 自訂時間地點驗證（改為選填，移除必填檢查）
    // 如果有填寫內容，可以在這裡添加格式驗證（目前無特殊格式要求）

    // 參加方式驗證
    if (!formData.participationType) {
      newErrors.participationType = '請選擇參加方式';
    }

    // 個人資訊驗證
    if (!formData.name.trim()) {
      newErrors.name = '請填寫姓名';
    }

    if (!formData.email.trim()) {
      newErrors.email = '請填寫Email';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email格式不正確';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = '請填寫手機號碼';
    } else {
      const cleanPhone = formData.phone.replace(/\D/g, '');
      // 支援 09xxxxxxxx (10位) 或 886xxxxxxxxx (12位)
      if (!/^(09\d{8}|886\d{9,10})$/.test(cleanPhone)) {
        newErrors.phone = '手機號碼格式不正確（請輸入09開頭或+886開頭的手機號碼）';
      }
    }

    // 同行者資訊驗證
    if (formData.participationType === 'pair') {
      if (!formData.companionName.trim()) {
        newErrors.companionName = '請填寫同行者姓名';
      }
      if (!formData.companionEmail.trim()) {
        newErrors.companionEmail = '請填寫同行者Email';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.companionEmail)) {
        newErrors.companionEmail = '同行者Email格式不正確';
      }
    }

    // 基本資訊驗證
    if (!formData.gender) {
      newErrors.gender = '請選擇性別';
    }
    if (!formData.age) {
      newErrors.age = '請選擇年齡';
    }
    if (!formData.region) {
      newErrors.region = '請選擇居住地區';
    }

    // 手錶類型驗證
    if (formData.watchTypes.length === 0) {
      newErrors.watchTypes = '請選擇至少一種手錶類型';
    }

    // 手錶品牌驗證
    if (!formData.watchTypes.includes('無') && formData.watchTypes.length > 0 && !formData.watchBrands.trim()) {
      newErrors.watchBrands = '請填寫手錶品牌';
    }

    // 同意條款驗證
    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = '請同意個人資料使用條款';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 即時驗證函數
  const validateField = (fieldName: string, value: string | boolean) => {
    const newErrors = { ...errors };

    switch (fieldName) {
      case 'email':
        if (typeof value === 'string') {
          if (!value.trim()) {
            newErrors.email = '請填寫Email';
          } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            newErrors.email = 'Email格式不正確';
          } else {
            delete newErrors.email;
          }
        }
        break;
      case 'phone':
        if (typeof value === 'string') {
          if (!value.trim()) {
            newErrors.phone = '請填寫手機號碼';
          } else {
            // 移除所有非數字字符
            const cleanPhone = value.replace(/\D/g, '');
            // 支援 09xxxxxxxx (10位) 或 886xxxxxxxxx (12位)
            if (!/^(09\d{8}|886\d{9,10})$/.test(cleanPhone)) {
              newErrors.phone = '手機號碼格式不正確（請輸入09開頭或+886開頭的手機號碼）';
            } else {
              delete newErrors.phone;
            }
          }
        }
        break;
      case 'companionEmail':
        if (formData.participationType === 'pair' && typeof value === 'string') {
          if (!value.trim()) {
            newErrors.companionEmail = '請填寫同行者Email';
          } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            newErrors.companionEmail = '同行者Email格式不正確';
          } else {
            delete newErrors.companionEmail;
          }
        }
        break;
    }

    setErrors(newErrors);
  };

  // 檢查表單是否有效
  useEffect(() => {
    // 檢查必填欄位是否已填寫且格式正確
    const hasRequiredFields =
      formData.sessionTimes.length > 0 &&
      formData.participationType &&
      formData.name.trim() &&
      formData.email.trim() &&
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email) &&
      formData.phone.trim() &&
      /^(09\d{8}|886\d{9,10})$/.test(formData.phone.replace(/\D/g, '')) &&
      formData.gender &&
      formData.age &&
      formData.region &&
      formData.watchTypes.length > 0 &&
      formData.agreeToTerms &&
      // 條件性必填欄位（customTimeLocation 改為選填）
      (formData.participationType !== 'pair' || (
        formData.companionName.trim() &&
        formData.companionEmail.trim() &&
        /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.companionEmail)
      )) &&
      (formData.watchTypes.includes('無') || formData.watchBrands.trim());

    setIsFormValid(Boolean(hasRequiredFields));
  }, [formData]);

  // 表單提交處理
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // 安全驗證：檢查蜜罐欄位和提交時間
    const securityValidation = validateBeforeSubmit();
    if (!securityValidation.isValid) {
      alert(securityValidation.reason || '表單驗證失敗，請重新整理頁面後再試');
      return;
    }

    // 檢查選擇的場次是否還有名額
    const unavailableSessions = formData.sessionTimes.filter(session =>
      session !== '有意願但無合適時間地點' && !isSessionAvailable(availability, session)
    );

    if (unavailableSessions.length > 0) {
      alert(`很抱歉，以下場次已額滿，請重新選擇：\n${unavailableSessions.join('\n')}`);
      return;
    }

    setIsSubmitting(true);

    try {
      // 判斷是否需要付款
      const needsPayment = !formData.sessionTimes.includes('有意願但無合適時間地點');

      // 計算價格：個人報名 1500，雙人團報 3000
      const eventPrice = needsPayment ? (formData.participationType === 'pair' ? 3000 : eventConfig.price) : 0;

      const response = await fetch('/api/event-registration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          eventName: eventConfig.name,
          eventPrice: eventPrice,
          submittedAt: (() => {
            const now = new Date();
            const utc8Time = new Date(now.getTime() + (8 * 60 * 60 * 1000));
            return utc8Time.toISOString().replace('Z', '+08:00');
          })(),
          needsPayment,
          // 添加 UTM 參數
          utmParams,
          // 添加安全驗證資料
          security: {
            honeypotValue,
            formStartTime,
            securityToken,
            submissionTime: Date.now(),
            userAgent: navigator.userAgent,
          },
        }),
      });

      const result = await response.json();

      if (response.ok) {
        if (result.needsPayment) {
          // 需要付款，跳轉到付款頁面
          alert('報名資料已送出，請繼續完成付款');
          window.location.href = `/payment/checkout?orderNo=${result.orderNo}`;
        } else {
          // 純資料收集，顯示完成訊息
          alert('感謝您的填寫！很可惜這次時間未能配合上，歡迎持續關注我們，之後有任何新的場次，會再優先通知您，謝謝');
          // 重置表單
          setFormData({
            sessionTimes: [],
            customTimeLocation: '',
            participationType: '',
            name: '',
            email: '',
            phone: '',
            companionName: '',
            companionEmail: '',
            gender: '',
            age: '',
            region: '',
            watchTypes: [],
            watchBrands: '',
            questions: '',
            agreeToTerms: false,
          });
          // 重置安全狀態
          resetSecurity();
        }
      } else {
        throw new Error(result.error || '提交失敗');
      }
    } catch (error) {
      console.error('提交錯誤:', error);
      alert(error instanceof Error ? error.message : '提交失敗，請稍後再試或聯繫客服。');
    } finally {
      setIsSubmitting(false);
    }
  };



  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-white py-8 md:py-12">
        <div className="max-w-7xl mx-auto px-4 md:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 items-center">
            {/* 左側圖片 */}
            <ScrollTriggerMotion
              variants={contentScrollVariants}
              className="order-2 md:order-1 relative"
            >
              <div className="w-full max-w-md mx-auto md:max-w-none">
                <Image
                  src="https://assets.softr-files.com/applications/78e28c86-637a-44cc-ac94-95ca3c49bdcc/assets/998deb69-879f-4433-abc6-c941317b11b4.png"
                  alt="錶匠體驗機芯拆解"
                  width={600}
                  height={400}
                  className="w-full h-auto object-cover rounded-lg shadow-lg"
                />
              </div>
            </ScrollTriggerMotion>

            {/* 右側內容 */}
            <div className="order-1 md:order-2">
              <div className="text-center md:text-left">
                {/* Main Title */}
                {isIOSSafari ? (
                  <div className="mb-4">
                    <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold tracking-tight" style={{ color: '#2b354d' }}>
                      {eventConfig.name}
                    </h1>
                  </div>
                ) : (
                  <ScrollTriggerMotion
                    variants={titleScrollVariants}
                    className="mb-4"
                  >
                    <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold tracking-tight" style={{ color: '#2b354d' }}>
                      {eventConfig.name}
                    </h1>
                  </ScrollTriggerMotion>
                )}

                {/* Description */}
                <ScrollTriggerMotion
                  variants={contentScrollVariants}
                  className="text-sm md:text-base mb-6 leading-relaxed"
                  style={{ color: '#2b354d' }}
                >
                  <p>
                    錶匠體驗希望讓更多對機械錶有興趣的人，有機會體驗手錶師傅的日常，親手接觸手錶機芯。新的系列課程預計透過四個部分讓大家完整認識一顆機芯，包括<strong>動力傳導</strong>、<strong>擺輪擒縱</strong>、<strong>調時結構</strong>以及<strong>面盤指針</strong>，進而了解其運作原理。
                  </p>
                </ScrollTriggerMotion>

                {/* CTA Button */}
                <ScrollTriggerMotion
                  variants={contentScrollVariants}
                  delay={0.2}
                  className="flex justify-center md:justify-start"
                >
                  <button
                    onClick={() => {
                      const formSection = document.getElementById('registration-form');
                      formSection?.scrollIntoView({ behavior: 'smooth' });
                    }}
                    className="bg-[#2b354d] text-white hover:bg-[#1e2a3a] font-semibold py-3 px-5 rounded-lg text-sm transition-all duration-300 border-0 shadow-lg hover:shadow-xl cursor-pointer"
                  >
                    立即報名
                  </button>
                </ScrollTriggerMotion>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 活動介紹 Section - 動態配置驅動 */}
      <section className="py-10 md:py-14" style={{ backgroundColor: '#2b354d' }}>
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* 主要介紹文字 */}
          <div className="text-center mb-10">
            {isIOSSafari ? (
              <div className="mb-6">
                <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white">
                  活動介紹
                </h2>
              </div>
            ) : (
              <ScrollTriggerMotion
                variants={titleScrollVariants}
                className="mb-6"
              >
                <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white">
                  活動介紹
                </h2>
              </ScrollTriggerMotion>
            )}
            <ScrollTriggerMotion
              variants={contentScrollVariants}
              className="text-sm md:text-base leading-relaxed text-white max-w-4xl mx-auto"
            >
              <p>
                你是否曾好奇，當輕輕轉動錶冠時，究竟是什麼力量在推動時間前進？我們特別設計了這系列課程，從「動力傳導」開始，帶你親手拆解手上鏈機芯，感受發條盒儲能與齒輪傳動的微妙變化。無論你是機械錶愛好者或初次接觸，這裡都是一場難得的時光之旅，誠摯邀請你加入我們，共同探索時間背後的奧秘。
              </p>
            </ScrollTriggerMotion>
          </div>

          {/* 動態內容區塊 - 根據配置自動調整佈局 */}
          <div className={`grid gap-6 md:gap-8 ${
            activitySections.length === 1 ? 'grid-cols-1 max-w-2xl mx-auto' :
            activitySections.length === 2 ? 'grid-cols-1 lg:grid-cols-2' :
            activitySections.length === 3 ? 'grid-cols-1 lg:grid-cols-3' :
            'grid-cols-1 lg:grid-cols-4'
          }`}>
            {activitySections.map((section, sectionIndex) => {
              const IconComponent = IconComponents[section.icon as keyof typeof IconComponents];
              return (
                <ScrollTriggerMotion
                  key={section.id}
                  variants={contentScrollVariants}
                  delay={sectionIndex * 0.1}
                  className="p-4 md:p-6"
                >
                  <div className="flex items-center mb-4">
                    <IconComponent
                      className="w-6 h-6 md:w-8 md:h-8 lg:w-8 lg:h-8 mr-3 flex-shrink-0"
                      style={{ color: '#f59e0b' }}
                    />
                    <h3 className="text-xl md:text-2xl lg:text-3xl font-bold text-white">{section.title}</h3>
                  </div>
                  <div className="space-y-3 text-white text-left">
                    {section.points.map((point, index) => (
                      <div key={index} className="flex items-start">
                        <ArrowRight
                          className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                          style={{ color: '#f59e0b' }}
                        />
                        <p className="text-sm md:text-base">{point}</p>
                      </div>
                    ))}
                  </div>
                </ScrollTriggerMotion>
              );
            })}
          </div>
        </div>
      </section>

      {/* 活動費用 & 活動地點 Section */}
      <section className="py-10 md:py-14 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`grid gap-6 md:gap-8 ${
            activitySections.length === 1 ? 'grid-cols-1 max-w-2xl mx-auto' :
            activitySections.length === 2 ? 'grid-cols-1 lg:grid-cols-2' :
            activitySections.length === 3 ? 'grid-cols-1 lg:grid-cols-3' :
            'grid-cols-1 lg:grid-cols-4'
          }`}>
            {/* 左側：活動費用 - 對齊動力傳導 */}
            <ScrollTriggerMotion
              variants={contentScrollVariants}
              className="p-4 md:p-6 movement-assembling-cost-section"
            >
              <div className="flex items-center mb-4">
                <CircleDollarSign
                  className="w-6 h-6 md:w-8 md:h-8 lg:w-8 lg:h-8 mr-3 flex-shrink-0"
                  style={{ color: '#f59e0b' }}
                />
                <h3 className="text-xl md:text-2xl lg:text-3xl font-bold" style={{ color: '#2b354d' }}>活動費用</h3>
              </div>

              <div className="space-y-3 text-left">
                <div className="flex items-start">
                  <CheckCircle
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <span className="text-sm md:text-base" style={{ color: '#2b354d' }}>NT$1500 / 人</span>
                </div>

                <div className="flex items-start">
                  <CheckCircle
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <span className="text-sm md:text-base" style={{ color: '#2b354d' }}>至多 8 位 / 場</span>
                </div>

                <div className="flex items-start">
                  <CheckCircle
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <span className="text-sm md:text-base" style={{ color: '#2b354d' }}>滿 4 人開班</span>
                </div>
              </div>
            </ScrollTriggerMotion>

            {/* 右側：活動地點 - 對齊擺輪擒縱 */}
            <ScrollTriggerMotion
              variants={contentScrollVariants}
              delay={0.1}
              className="p-4 md:p-6 movement-assembling-location-section"
            >
              <div className="flex items-center mb-4">
                <MapPin
                  className="w-6 h-6 md:w-8 md:h-8 lg:w-8 lg:h-8 mr-3 flex-shrink-0"
                  style={{ color: '#f59e0b' }}
                />
                <h3 className="text-xl md:text-2xl lg:text-3xl font-bold" style={{ color: '#2b354d' }}>活動地點</h3>
              </div>

              <div className="space-y-3 text-left">
                <div className="flex items-start">
                  <MapPin
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <a
                    href="https://maps.google.com/?q=台北市信義區光復南路501號3F"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm md:text-base hover:underline"
                    style={{ color: '#2b354d' }}
                  >
                    台北市信義區光復南路 501 號 3F
                  </a>
                </div>

                <div className="flex items-start">
                  <MapPin
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <a
                    href="https://maps.google.com/?q=台北市東區公園路130號2F"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm md:text-base hover:underline"
                    style={{ color: '#2b354d' }}
                  >
                    台北市東區公園路 130 號 2F
                  </a>
                </div>

                <div className="flex items-start">
                  <MapPin
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <a
                    href="https://maps.google.com/?q=高雄市新興區民權一路251號27F"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm md:text-base hover:underline"
                    style={{ color: '#2b354d' }}
                  >
                    高雄市新興區民權一路 251 號 27F
                  </a>
                </div>
              </div>
            </ScrollTriggerMotion>
          </div>
        </div>
      </section>

      {/* 活動場次 & 注意事項 Section */}
      <section className="py-10 md:py-14" style={{ backgroundColor: '#2b354d' }}>
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-10">
            {/* 左側：活動場次 */}
            <ScrollTriggerMotion
              variants={contentScrollVariants}
              className="p-4 md:p-6"
            >
              <div className="flex items-center mb-4">
                <Calendar
                  className="w-6 h-6 md:w-8 md:h-8 lg:w-8 lg:h-8 mr-3 flex-shrink-0"
                  style={{ color: '#f59e0b' }}
                />
                <h3 className="text-xl md:text-2xl lg:text-3xl font-bold text-white">活動場次</h3>
              </div>

              <div className="space-y-3 text-left">
                <div className="flex items-start mb-4">
                  <Info
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <span className="text-white text-sm md:text-base">台中萬華場次主題為動力傳導，信義場次主題為擺輪擒縱</span>
                </div>

                <div className="flex items-start">
                  <Clock
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <span className="text-white text-sm md:text-base">台中 07/18（五）19:20 - 20:50</span>
                </div>

                <div className="flex items-start">
                  <Clock
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <span className="text-white text-sm md:text-base">台北 07/19（六）13:20 - 14:50（萬華）</span>
                </div>

                <div className="flex items-start">
                  <Clock
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <span className="text-white text-sm md:text-base">台北 07/20（日）13:20 - 14:50（萬華）</span>
                </div>

                <div className="flex items-start">
                  <Clock
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <span className="text-white text-sm md:text-base">台北 07/20（日）15:20 - 16:50（萬華）</span>
                </div>

                <div className="flex items-start">
                  <Clock
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <span className="text-white text-sm md:text-base">台北 07/25（五）19:20 - 20:50（信義）</span>
                </div>

                <div className="flex items-start">
                  <Clock
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <span className="text-white text-sm md:text-base">台北 07/26（六）13:20 - 14:50（信義）</span>
                </div>

                <div className="flex items-start">
                  <Clock
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <span className="text-white text-sm md:text-base">台北 07/26（六）15:20 - 16:50（信義）</span>
                </div>
              </div>
            </ScrollTriggerMotion>

            {/* 右側：注意事項 */}
            <ScrollTriggerMotion
              variants={contentScrollVariants}
              delay={0.1}
              className="p-4 md:p-6"
            >
              <div className="flex items-center mb-4">
                <Info
                  className="w-6 h-6 md:w-8 md:h-8 lg:w-8 lg:h-8 mr-3 flex-shrink-0"
                  style={{ color: '#f59e0b' }}
                />
                <h3 className="text-xl md:text-2xl lg:text-3xl font-bold text-white">注意事項</h3>
              </div>

              <div className="space-y-3 text-white text-left">
                <div className="flex items-start">
                  <Info
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <p className="text-sm md:text-base leading-relaxed">
                    報名成功以收到電子郵件（請留意信箱填寫正確或垃圾郵件）為主，裡面會包含付款連結。送出表單後最遲將於 2 日內通知報名結果
                  </p>
                </div>

                <div className="flex items-start">
                  <Info
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <p className="text-sm md:text-base leading-relaxed">
                    付款後對帳完成後將寄送入場通知信至電子信箱，活動時間地點若有更改將會提前通知
                  </p>
                </div>

                <div className="flex items-start">
                  <Info
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <p className="text-sm md:text-base leading-relaxed">
                    活動時間約為九十分鐘，當天依場次時間，憑信件通知入場
                  </p>
                </div>

                <div className="flex items-start">
                  <Info
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <p className="text-sm md:text-base leading-relaxed">
                    報名後若因個人因素取消，活動日 8 天（含）前僅扣除行政手續費 50 元之剩餘金額退款，3 天（含）前退款 50%，1 天（含）前退款 30%，活動當日取消恕不予退款
                  </p>
                </div>

                <div className="flex items-start">
                  <Info
                    className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0"
                    style={{ color: '#f59e0b' }}
                  />
                  <p className="text-sm md:text-base leading-relaxed">
                    體驗使用的機芯及工具由主辦單位提供，機芯僅做為體驗使用無法帶走
                  </p>
                </div>
              </div>
            </ScrollTriggerMotion>
          </div>
        </div>
      </section>

      {/* Registration Form Section - 使用 shadcn/ui 設計系統重構 */}
      <section id="registration-form" className="py-12 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {isIOSSafari ? (
            <Card className="shadow-xl border border-slate-200 bg-white">
              <CardHeader className="text-center pb-8">
                <CardTitle className="text-2xl md:text-3xl lg:text-4xl font-bold" style={{ color: '#2b354d' }}>
                  錶匠體驗報名
                </CardTitle>
              </CardHeader>
              <CardContent className="movement-assembling-form">
          ) : (
            <ScrollTriggerMotion
              variants={contentScrollVariants}
            >
              <Card className="shadow-xl border border-slate-200 bg-white">
                <CardHeader className="text-center pb-8">
                  <ScrollTriggerMotion
                    variants={titleScrollVariants}
                  >
                    <CardTitle className="text-2xl md:text-3xl lg:text-4xl font-bold" style={{ color: '#2b354d' }}>
                      錶匠體驗報名
                    </CardTitle>
                  </ScrollTriggerMotion>
                </CardHeader>
                <CardContent className="movement-assembling-form">
          )}
              <form className="space-y-8" onSubmit={handleSubmit}>
                {/* 安全防護欄位 - 對使用者隱藏 */}
                <HoneypotField
                  value={honeypotValue}
                  onChange={setHoneypotValue}
                />
                <TimestampField startTime={formStartTime} />
                <SecurityTokenField token={securityToken} />

                {/* 場次時間選擇 */}
                <Card className="border-slate-200 bg-white">
                  <CardHeader className="pb-0">
                    <CardTitle className="text-lg font-semibold mb-2" style={{ color: '#2b354d' }}>
                      場次時間 <span className="text-red-500">*</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {/* 載入狀態 */}
                    {availabilityLoading && (
                      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <p className="text-blue-600 text-sm">🔄 正在載入場次名額資訊...</p>
                      </div>
                    )}

                    {/* 錯誤狀態 */}
                    {availabilityError && (
                      <div className="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                        <p className="text-amber-600 text-sm">⚠️ 無法載入場次名額資訊，請稍後再試</p>
                      </div>
                    )}

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                      {eventConfig.sessionOptions.map((option: string, index: number) => {
                        // 獲取場次可用性資訊
                        const sessionAvailable = isSessionAvailable(availability, option);
                        const availabilityText = getAvailabilityText(availability, option);
                        const isDisabled = !sessionAvailable && option !== '有意願但無合適時間地點';

                        return (
                          <label
                            key={index}
                            className={`flex items-center justify-between p-4 border rounded-lg transition-all ${
                              isDisabled
                                ? 'border-slate-200 bg-slate-50 cursor-not-allowed opacity-60'
                                : 'border-slate-200 hover:border-slate-300 hover:bg-slate-50 cursor-pointer'
                            } group`}
                          >
                            <div className="flex items-center">
                              <input
                                type="checkbox"
                                name="sessionTimes"
                                value={option}
                                className="w-4 h-4 border-slate-300 rounded focus:ring-2 transition-colors"
                                style={{
                                  accentColor: '#2b354d'
                                } as React.CSSProperties}
                                checked={formData.sessionTimes.includes(option)}
                                disabled={isDisabled}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    if (option === '有意願但無合適時間地點') {
                                      // 如果選擇"有意願但無合適時間地點"，清除其他所有選項
                                      setFormData(prev => ({
                                        ...prev,
                                        sessionTimes: [option]
                                      }));
                                    } else {
                                      // 如果選擇其他選項，先移除"有意願但無合適時間地點"，再添加新選項
                                      setFormData(prev => ({
                                        ...prev,
                                        sessionTimes: [...prev.sessionTimes.filter(time => time !== '有意願但無合適時間地點'), option]
                                      }));
                                    }
                                  } else {
                                    setFormData(prev => ({
                                      ...prev,
                                      sessionTimes: prev.sessionTimes.filter(time => time !== option)
                                    }));
                                  }
                                }}
                              />
                              <span className={`ml-3 font-medium transition-colors ${
                                isDisabled
                                  ? 'text-slate-400'
                                  : 'text-slate-700 group-hover:text-slate-900'
                              }`}>
                                {option}
                              </span>
                            </div>

                            {/* 顯示名額資訊 */}
                            {availabilityText && (
                              <span className={`text-sm font-medium px-2 py-1 rounded ${
                                availabilityText === '已額滿'
                                  ? 'bg-red-100 text-red-600'
                                  : 'bg-amber-100 text-amber-600'
                              }`}>
                                {availabilityText}
                              </span>
                            )}
                          </label>
                        );
                      })}
                    </div>
                    {errors.sessionTimes && (
                      <p className="text-red-500 text-sm mt-3" data-testid="error-session">{errors.sessionTimes}</p>
                    )}
                  </CardContent>
                </Card>

                {/* 自訂時間地點 (條件顯示) */}
                {formData.sessionTimes.includes('有意願但無合適時間地點') && (
                  <Card className="border-amber-200 bg-amber-50">
                    <CardHeader className="pb-0">
                      <CardTitle className="text-lg font-semibold mb-2" style={{ color: '#2b354d' }}>
                        期待的時間與地區
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <textarea
                        className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent resize-none bg-white"
                        style={{
                          '--tw-ring-color': '#2b354d'
                        } as React.CSSProperties}
                        rows={3}
                        placeholder="高雄平日晚上、台北一晚五晚......"
                        value={formData.customTimeLocation}
                        onChange={(e) => setFormData(prev => ({ ...prev, customTimeLocation: e.target.value }))}
                      />
                      <p className="text-slate-600 text-sm mt-3">
                        我們會依照填答狀況做為日後場次安排參考並優先通知您
                      </p>
                      {errors.customTimeLocation && (
                        <p className="text-red-500 text-sm mt-3">{errors.customTimeLocation}</p>
                      )}
                    </CardContent>
                  </Card>
                )}

                {/* 參加方式 */}
                <Card className="border-slate-200 bg-white">
                  <CardHeader className="pb-0">
                    <CardTitle className="text-lg font-semibold mb-2" style={{ color: '#2b354d' }}>
                      參加方式 <span className="text-red-500">*</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      {participationTypeOptions.map((option) => (
                        <label key={option.value} className="flex items-center justify-center p-4 border border-slate-200 rounded-lg hover:border-slate-300 hover:bg-slate-50 transition-all cursor-pointer group min-h-[60px]">
                          <input
                            type="radio"
                            name="participationType"
                            value={option.value}
                            className="w-4 h-4 border-slate-300 focus:ring-2 transition-colors"
                            style={{
                              accentColor: '#2b354d'
                            } as React.CSSProperties}
                            checked={formData.participationType === option.value}
                            onChange={(e) => setFormData(prev => ({ ...prev, participationType: e.target.value as 'individual' | 'pair' | '' }))}
                          />
                          <span className="ml-3 text-slate-700 font-medium group-hover:text-slate-900 transition-colors text-center flex-1">{option.label}</span>
                        </label>
                      ))}
                    </div>
                    {errors.participationType && (
                      <p className="text-red-500 text-sm mt-3">{errors.participationType}</p>
                    )}
                  </CardContent>
                </Card>

                {/* 個人資訊 */}
                <Card className="border-slate-200 bg-white">
                  <CardHeader className="pb-0">
                    <CardTitle className="text-lg font-semibold mb-2" style={{ color: '#2b354d' }}>個人資訊</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* 姓名 */}
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          姓名 <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          name="name"
                          className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent bg-white transition-colors"
                          style={{
                            '--tw-ring-color': '#2b354d'
                          } as React.CSSProperties}
                          placeholder="王大明"
                          value={formData.name}
                          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        />
                        {errors.name && (
                          <p className="text-red-500 text-sm mt-2" data-testid="error-name">{errors.name}</p>
                        )}
                      </div>

                      {/* Email */}
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          Email <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="email"
                          name="email"
                          className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent bg-white transition-colors"
                          style={{
                            '--tw-ring-color': '#2b354d'
                          } as React.CSSProperties}
                          placeholder="<EMAIL>"
                          value={formData.email}
                          onChange={(e) => {
                            setFormData(prev => ({ ...prev, email: e.target.value }));
                            validateField('email', e.target.value);
                          }}
                          onBlur={(e) => validateField('email', e.target.value)}
                        />
                        <p className="text-slate-600 text-xs mt-2">
                          報名結果將主要以信件通知，請務必填寫正確以利通知
                        </p>
                        {errors.email && (
                          <p className="text-red-500 text-sm mt-2" data-testid="error-email">{errors.email}</p>
                        )}
                      </div>
                    </div>

                    {/* 手機 */}
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        手機 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        autoComplete="tel"
                        className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent bg-white transition-colors"
                        style={{
                          '--tw-ring-color': '#2b354d'
                        } as React.CSSProperties}
                        placeholder="0912345678"
                        value={formData.phone}
                        onChange={(e) => {
                          setFormData(prev => ({ ...prev, phone: e.target.value }));
                          validateField('phone', e.target.value);
                        }}
                        onBlur={(e) => validateField('phone', e.target.value)}
                      />
                      <p className="text-slate-600 text-xs mt-2">
                        若信件未連絡上會再以手機聯繫
                      </p>
                      {errors.phone && (
                        <p className="text-red-500 text-sm mt-2" data-testid="error-phone">{errors.phone}</p>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* 同行者資訊 (條件顯示) */}
                {formData.participationType === 'pair' && (
                  <Card className="border-amber-200 bg-amber-50">
                    <CardHeader className="pb-0">
                      <CardTitle className="text-lg font-semibold mb-1" style={{ color: '#2b354d' }}>同行者資訊</CardTitle>
                      <p className="text-slate-600 text-sm mb-2">
                        您選擇了雙人團報，請填寫同行者的姓名與信箱以利通知
                      </p>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* 同行者姓名 */}
                        <div>
                          <label className="block text-sm font-medium text-slate-700 mb-2">
                            同行者姓名 <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent bg-white transition-colors"
                            style={{
                              '--tw-ring-color': '#2b354d'
                            } as React.CSSProperties}
                            placeholder="王大明"
                            value={formData.companionName}
                            onChange={(e) => setFormData(prev => ({ ...prev, companionName: e.target.value }))}
                          />
                          {errors.companionName && (
                            <p className="text-red-500 text-sm mt-2">{errors.companionName}</p>
                          )}
                        </div>

                        {/* 同行者 Email */}
                        <div>
                          <label className="block text-sm font-medium text-slate-700 mb-2">
                            同行者 Email <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="email"
                            className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent bg-white transition-colors"
                            style={{
                              '--tw-ring-color': '#2b354d'
                            } as React.CSSProperties}
                            placeholder="<EMAIL>"
                            value={formData.companionEmail}
                            onChange={(e) => {
                              setFormData(prev => ({ ...prev, companionEmail: e.target.value }));
                              validateField('companionEmail', e.target.value);
                            }}
                            onBlur={(e) => validateField('companionEmail', e.target.value)}
                          />
                          {errors.companionEmail && (
                            <p className="text-red-500 text-sm mt-2">{errors.companionEmail}</p>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* 基本資訊 */}
                <Card className="border-slate-200 bg-white">
                  <CardHeader className="pb-0">
                    <CardTitle className="text-lg font-semibold mb-2" style={{ color: '#2b354d' }}>基本資訊</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {/* 性別 */}
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-3">
                          性別 <span className="text-red-500">*</span>
                        </label>
                        <div className="space-y-2 md:space-y-0 md:flex md:gap-6">
                          {genderOptions.map((option) => (
                            <label key={option.value} className="flex items-center cursor-pointer">
                              <input
                                type="radio"
                                name="gender"
                                value={option.value}
                                className="w-4 h-4 border-slate-300 focus:ring-2 transition-colors"
                                style={{
                                  accentColor: '#2b354d'
                                } as React.CSSProperties}
                                checked={formData.gender === option.value}
                                onChange={(e) => setFormData(prev => ({ ...prev, gender: e.target.value as 'male' | 'female' | 'other' }))}
                              />
                              <span className="ml-2 text-slate-700">{option.label}</span>
                            </label>
                          ))}
                        </div>
                        {errors.gender && (
                          <p className="text-red-500 text-sm mt-2">{errors.gender}</p>
                        )}
                      </div>

                      {/* 年齡 */}
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          年齡 <span className="text-red-500">*</span>
                        </label>
                        <select
                          className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent bg-white transition-colors"
                          style={{
                            '--tw-ring-color': '#2b354d'
                          } as React.CSSProperties}
                          value={formData.age}
                          onChange={(e) => setFormData(prev => ({ ...prev, age: e.target.value as '<20' | '20-29' | '30-39' | '40-49' | '50-59' | '>=60' | '' }))}
                        >
                          <option value="">請選擇年齡</option>
                          {ageOptions.map((option) => (
                            <option key={option.value} value={option.value}>{option.label}</option>
                          ))}
                        </select>
                        {errors.age && (
                          <p className="text-red-500 text-sm mt-2">{errors.age}</p>
                        )}
                      </div>

                      {/* 居住地區 */}
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          居住地區 <span className="text-red-500">*</span>
                        </label>
                        <select
                          className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent bg-white transition-colors"
                          style={{
                            '--tw-ring-color': '#2b354d'
                          } as React.CSSProperties}
                          value={formData.region}
                          onChange={(e) => setFormData(prev => ({ ...prev, region: e.target.value as 'north' | 'central' | 'south' | 'offshore' | 'other' | '' }))}
                        >
                          <option value="">請選擇地區</option>
                          {regionOptions.map((option) => (
                            <option key={option.value} value={option.value}>{option.label}</option>
                          ))}
                        </select>
                        {errors.region && (
                          <p className="text-red-500 text-sm mt-2">{errors.region}</p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* 手錶相關資訊 */}
                <Card className="border-slate-200 bg-white">
                  <CardHeader className="pb-0">
                    <CardTitle className="text-lg font-semibold mb-2" style={{ color: '#2b354d' }}>手錶相關資訊</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* 擁有的手錶類型 */}
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-3">
                        擁有的手錶類型 <span className="text-red-500">*</span>
                      </label>
                      <div className="grid grid-cols-2 lg:grid-cols-5 gap-3">
                        {watchTypeOptions.map((option) => (
                          <label key={option} className="flex items-center p-3 border border-slate-200 rounded-lg hover:border-slate-300 hover:bg-slate-50 transition-all cursor-pointer group">
                            <input
                              type="checkbox"
                              className="w-4 h-4 border-slate-300 rounded focus:ring-2 transition-colors"
                              style={{
                                accentColor: '#2b354d'
                              } as React.CSSProperties}
                              checked={formData.watchTypes.includes(option)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  if (option === '無') {
                                    // 如果選擇"無"，清除其他所有選項
                                    setFormData(prev => ({
                                      ...prev,
                                      watchTypes: [option]
                                    }));
                                  } else {
                                    // 如果選擇其他選項，先移除"無"，再添加新選項
                                    setFormData(prev => ({
                                      ...prev,
                                      watchTypes: [...prev.watchTypes.filter(type => type !== '無'), option]
                                    }));
                                  }
                                } else {
                                  setFormData(prev => ({
                                    ...prev,
                                    watchTypes: prev.watchTypes.filter(type => type !== option)
                                  }));
                                }
                              }}
                            />
                            <span className="ml-2 text-sm text-slate-700 group-hover:text-slate-900 transition-colors">{option}</span>
                          </label>
                        ))}
                      </div>
                      {errors.watchTypes && (
                        <p className="text-red-500 text-sm mt-3">{errors.watchTypes}</p>
                      )}
                    </div>

                    {/* 目前擁有手錶的品牌 (條件顯示) */}
                    {!formData.watchTypes.includes('無') && formData.watchTypes.length > 0 && (
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          目前擁有手錶的品牌 <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent bg-white transition-colors"
                          style={{
                            '--tw-ring-color': '#2b354d'
                          } as React.CSSProperties}
                          placeholder="Rolex、Seiko......"
                          value={formData.watchBrands}
                          onChange={(e) => setFormData(prev => ({ ...prev, watchBrands: e.target.value }))}
                        />
                        {errors.watchBrands && (
                          <p className="text-red-500 text-sm mt-2">{errors.watchBrands}</p>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* 對於活動有任何疑問 */}
                <Card className="border-slate-200 bg-white">
                  <CardHeader className="pb-0">
                    <CardTitle className="text-lg font-semibold mb-2" style={{ color: '#2b354d' }}>對於活動有任何疑問</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <textarea
                      className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent resize-none bg-white transition-colors"
                      style={{
                        '--tw-ring-color': '#2b354d'
                      } as React.CSSProperties}
                      rows={4}
                      placeholder="歡迎與我們分享您對活動的疑問或興趣，我們將會盡速回覆您"
                      value={formData.questions}
                      onChange={(e) => setFormData(prev => ({ ...prev, questions: e.target.value }))}
                    />
                  </CardContent>
                </Card>

                {/* 個人資料使用與說明同意 */}
                <Card className="border-slate-200 bg-white">
                  <CardHeader className="pb-0">
                    <CardTitle className="text-lg font-semibold mb-2" style={{ color: '#2b354d' }}>個人資料使用與說明</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="bg-slate-50 p-6 rounded-lg border border-slate-200">
                      <div className="text-sm text-slate-600 space-y-3 leading-relaxed">
                        <p>1. 本報名表之個人資料之蒐集、處理及利用，係作為保險、手冊及聯繫等與本活動相關事項之用，由本單位依相關法令規定妥善保存保密。報名資料請確切由本人填寫，如偽造身份資料，一經查獲自負法律責任。</p>
                        <p>2. 參加者參與本活動即代表同意無償授權主辦單位上傳之照片及文字之肖像權及著作權，主辦單位為行銷、推廣用途得以重製、改作、公開傳輸、公開展示、公開發表等方式不限地點、時間、次數、方式永久使用上傳照片及文字。若現場不希望被攝影或是照片不希望用於未來宣傳可於備註留言或現場告知。</p>
                        <p>3. 除上述事項外，如有未盡事宜，主辦單位擁有保留修改或終止活動內容之權利，修改訊息將於本網站上公佈，不另行通知。</p>
                        <p>4. 當您送出問卷，即表示你已閱讀、瞭解，並同意接受本同意書之所有內容及其後修改變更規定。</p>
                      </div>
                    </div>

                    <label className="flex items-start gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        name="agreeTerms"
                        className="mt-1 w-4 h-4 border-slate-300 rounded focus:ring-2 transition-colors"
                        style={{
                          accentColor: '#2b354d'
                        } as React.CSSProperties}
                        checked={formData.agreeToTerms}
                        onChange={(e) => setFormData(prev => ({ ...prev, agreeToTerms: e.target.checked }))}
                      />
                      <span className="text-sm text-slate-700">
                        我同意 <span className="text-red-500">*</span>
                      </span>
                    </label>
                    {errors.agreeToTerms && (
                      <p className="text-red-500 text-sm">{errors.agreeToTerms}</p>
                    )}
                  </CardContent>
                </Card>

                {/* 提交按鈕 */}
                <div className="pt-6">
                  <button
                    type="submit"
                    disabled={!isFormValid || isSubmitting}
                    className={`w-full py-4 px-6 rounded-lg font-semibold text-lg transition-all duration-300 ${
                      isFormValid && !isSubmitting
                        ? 'bg-[#2b354d] text-white hover:bg-[#1e2a3a] shadow-lg hover:shadow-xl cursor-pointer'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    {isSubmitting ? (
                      <div className="flex items-center justify-center gap-2">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        提交中...
                      </div>
                    ) : (
                      '提交報名'
                    )}
                  </button>
                </div>
            </form>
              </CardContent>
            </Card>
          {!isIOSSafari && (
            </ScrollTriggerMotion>
          )}
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-12" style={{ backgroundColor: '#2b354d' }}>
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            {isIOSSafari ? (
              <div className="mb-4">
                <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white">
                  參加過的人怎麼說
                </h2>
              </div>
            ) : (
              <ScrollTriggerMotion
                variants={titleScrollVariants}
                className="mb-4"
              >
                <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white">
                  參加過的人怎麼說
                </h2>
              </ScrollTriggerMotion>
            )}
            <ScrollTriggerMotion
              variants={contentScrollVariants}
              className="text-white text-sm md:text-base"
            >
              <p>
                超過 <span className="text-yellow-400 font-semibold">50</span> 場 <span className="text-yellow-400 font-semibold">300</span> 多位來自全台各地的鐘錶愛好者親情參與，<span className="text-yellow-400 font-semibold">90%</span> 滿意度好評推薦
              </p>
            </ScrollTriggerMotion>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                name: "張先生",
                role: "機構工程師",
                content: "一直對機械錶很好奇，但沒什麼機會接觸。這個活動不僅能實際動手，也更認識運作的細節，推薦！",
                avatar: "/images/testimonials/chang.webp"
              },
              {
                name: "陳小姐",
                role: "工業設計系學生",
                content: "本身背景與設計有關，無意間看到機械錶相關活動，因此產生好奇，就算對錶沒這麼了解也能樂在其中",
                avatar: "/images/testimonials/chen.webp"
              },
              {
                name: "李先生",
                role: "精錶愛好者",
                content: "平時多在網路上看各種錶，但能親自動手拆裝零件還是第一次。課程安排得宜講解仔細，整個過程很有趣",
                avatar: "/images/testimonials/li.webp"
              }
            ].map((testimonial, index) => (
              <ScrollTriggerMotion
                key={index}
                variants={contentScrollVariants}
                delay={index * 0.1}
                className="text-center"
              >
                {/* Avatar */}
                <div className="w-16 h-16 mx-auto mb-4 rounded-full overflow-hidden">
                  <Image
                    src={testimonial.avatar}
                    alt={`${testimonial.name}的頭像`}
                    width={64}
                    height={64}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Content */}
                <div className="mb-4">
                  <p className="text-white leading-relaxed text-sm md:text-base">
                    「{testimonial.content}」
                  </p>
                </div>

                {/* Name and Role */}
                <div>
                  <p className="font-semibold text-white mb-1 text-sm">{testimonial.name}</p>
                  <p className="text-blue-200 text-xs">{testimonial.role}</p>
                </div>
              </ScrollTriggerMotion>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-12 bg-slate-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            {isIOSSafari ? (
              <div className="mb-4">
                <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-slate-900">
                  常見問題
                </h2>
              </div>
            ) : (
              <ScrollTriggerMotion
                variants={titleScrollVariants}
                className="mb-4"
              >
                <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-slate-900">
                  常見問題
                </h2>
              </ScrollTriggerMotion>
            )}
            <ScrollTriggerMotion
              variants={contentScrollVariants}
              className="text-slate-600 text-sm md:text-base"
            >
              <p>
                對於活動有任何疑問嗎？歡迎查看下方說明，若仍有其他問題，歡迎來信或私訊詢問！
              </p>
            </ScrollTriggerMotion>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                q: "臨時沒辦法參加會退費嗎？",
                a: "若因主辦單位因素導致無法參與，會全額退款；若因個人因素取消，活動日 8 天（含）前僅扣除行政手續費 50 元之剩餘金額退款，3 天（含）前退款 50%，1 天（含）前退款 30%，活動當日取消恕不予退款"
              },
              {
                q: "小朋友能夠參加嗎？",
                a: "本活動需要專注與穩定的操作，過程中會使用一字起子和鎳子等工具。如果小朋友能安靜參與，遵照指示操作，參與是沒有問題的，也歡迎家長一起來體驗"
              },
              {
                q: "參加活動需要準備什麼？",
                a: "不需要帶任何東西，活動所需的器材皆由我們準備，惟使用的機芯及工具僅做為教具體驗使用，無法帶走"
              }
            ].map((faq, index) => (
              <ScrollTriggerMotion
                key={index}
                variants={contentScrollVariants}
                delay={index * 0.1}
                className="bg-white rounded-lg p-4 shadow-sm"
              >
                <h3 className="font-semibold text-slate-900 mb-3 text-sm md:text-base">{faq.q}</h3>
                <p className="text-slate-600 leading-relaxed text-sm md:text-base">{faq.a}</p>
              </ScrollTriggerMotion>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 bg-gradient-to-r from-slate-800 to-slate-700 text-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          {isIOSSafari ? (
            <div className="mb-4">
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white">
                準備好開始你的鐘錶之旅了嗎？
              </h2>
            </div>
          ) : (
            <ScrollTriggerMotion
              variants={titleScrollVariants}
              className="mb-4"
            >
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white">
                準備好開始你的鐘錶之旅了嗎？
              </h2>
            </ScrollTriggerMotion>
          )}
          <ScrollTriggerMotion
            variants={contentScrollVariants}
            className="text-sm md:text-base text-slate-300 mb-6 max-w-2xl mx-auto"
          >
            <p>
              和我們一起探索機械錶的精密世界，體驗工藝之美
            </p>
          </ScrollTriggerMotion>
          <ScrollTriggerMotion
            variants={contentScrollVariants}
            delay={0.2}
          >
            <button
              onClick={() => {
                document.querySelector('form')?.scrollIntoView({
                  behavior: 'smooth',
                  block: 'start'
                });
              }}
              className="bg-white hover:bg-gray-50 font-semibold px-8 py-4 rounded-lg transition-colors duration-200 transform hover:scale-105"
              style={{ color: '#2b354d' }}
            >
              立即報名參加
            </button>
          </ScrollTriggerMotion>
        </div>
      </section>

    </div>
  );
}
