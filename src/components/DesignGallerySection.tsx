"use client";

import { useState, useCallback, useEffect } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { X, ChevronLeft, ChevronRight } from "lucide-react";
import { ScrollTriggerMotion, CardMotion } from "@/components/motion/MotionWrapper";
import { titleScrollVariants, contentScrollVariants, cardEntranceVariants } from "@/lib/motion-config";
import { useIsIOSSafari } from "@/hooks/useBrowserDetection";

interface GalleryImage {
  id: number;
  image: string;
  alt: string;
}

interface DesignGallerySectionProps {
  title: string;
  textContent: string[];
  images: GalleryImage[];
}

const DesignGallerySection = ({
  title,
  textContent,
  images
}: DesignGallerySectionProps) => {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const { isIOSSafari } = useIsIOSSafari();

  const openLightbox = (index: number) => {
    setCurrentImageIndex(index);
    setLightboxOpen(true);
  };

  const closeLightbox = () => {
    setLightboxOpen(false);
  };

  const nextImage = useCallback(() => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  }, [images.length]);

  const prevImage = useCallback(() => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  }, [images.length]);

  // 鍵盤事件監聽
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') closeLightbox();
      if (e.key === 'ArrowRight') nextImage();
      if (e.key === 'ArrowLeft') prevImage();
    };

    if (lightboxOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [lightboxOpen, images.length, nextImage, prevImage]);

  return (
    <>
      <section className="py-10 md:py-16 lg:py-18 bg-white design-gallery-section">
        {/* 針對 iPad Pro 優化的容器 - 減少左右填充 */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8 lg:gap-10 xl:gap-16 items-start lg:items-center">
            {/* 左側：文字內容 - 向內縮進對齊 */}
            <div className="space-y-6 text-center lg:text-left lg:pl-8 xl:pl-12">
              {isIOSSafari ? (
                <div className="mb-8">
                  <h2 className="text-2xl md:text-4xl font-bold text-[#2b354d]">
                    {title}
                  </h2>
                </div>
              ) : (
                <ScrollTriggerMotion
                  variants={titleScrollVariants}
                  className="mb-8"
                >
                  <h2 className="text-2xl md:text-4xl font-bold text-[#2b354d]">
                    {title}
                  </h2>
                </ScrollTriggerMotion>
              )}

              <ScrollTriggerMotion
                variants={contentScrollVariants}
                className="space-y-4"
              >
                <div>
                  {textContent.map((text, index) => (
                    <p
                      key={index}
                      className="text-base md:text-lg leading-relaxed mb-4"
                      style={{ color: '#2b354d' }}
                    >
                      {text}
                    </p>
                  ))}
                </div>
              </ScrollTriggerMotion>
            </div>

            {/* 右側：圖片 Gallery - 響應式置中對齊 */}
            {isIOSSafari ? (
              <div className="flex gap-2 md:gap-3 lg:gap-4 justify-center lg:justify-end xl:justify-end max-w-full items-start lg:pr-8 xl:pr-12">
                {/* 左側大圖 - iOS Safari 靜態版本 - 調整尺寸避免超出 */}
                <div
                  className="relative w-[240px] sm:w-[280px] md:w-[320px] lg:w-[340px] xl:w-[380px] aspect-[5/4] overflow-hidden rounded-xl cursor-pointer group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 flex-shrink-0"
                  onClick={() => openLightbox(0)}
                >
                  <Image
                    src={images[0].image}
                    alt={images[0].alt}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                    sizes="(max-width: 640px) 240px, (max-width: 768px) 280px, (max-width: 1024px) 320px, (max-width: 1280px) 340px, 380px"
                    priority
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
                  {/* 點擊提示 */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-white/90 rounded-full p-3 shadow-lg">
                      <svg className="w-6 h-6 text-[#2b354d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* 右側三張小圖垂直排列 - iOS Safari 靜態版本 */}
                <div className="flex flex-col justify-between gap-2 md:gap-3 lg:gap-4 w-24 sm:w-28 md:w-32 lg:w-40 xl:w-44 flex-shrink-0">
                  {images.slice(1).map((image, index) => (
                    <div
                      key={image.id}
                      className="relative aspect-[4/3] overflow-hidden rounded-lg cursor-pointer group hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 bg-gray-200"
                      onClick={() => openLightbox(index + 1)}
                      style={{ minHeight: '60px' }}
                    >
                      <Image
                        src={image.image}
                        alt={image.alt}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-500"
                        sizes="(max-width: 640px) 96px, (max-width: 768px) 112px, (max-width: 1024px) 128px, (max-width: 1280px) 160px, 176px"
                        priority={false}
                        loading="lazy"
                        onError={(e) => {
                          console.error('Image load error:', image.image);
                          e.currentTarget.style.display = 'none';
                        }}
                        onLoad={() => {
                          console.log('Image loaded successfully:', image.image);
                        }}
                      />
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
                      {/* 點擊提示 */}
                      <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="bg-white/90 rounded-full p-1.5 shadow-lg">
                          <svg className="w-3 h-3 md:w-4 md:h-4 text-[#2b354d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <ScrollTriggerMotion
                variants={contentScrollVariants}
                delay={0.3}
                className="flex gap-2 md:gap-3 lg:gap-4 justify-center lg:justify-end xl:justify-end max-w-full items-start lg:pr-8 xl:pr-12"
              >
                {/* 左側大圖 - 手機和平板都使用長方形比例 */}
                <CardMotion
                  index={0}
                  variants={cardEntranceVariants}
                  className="relative w-[280px] sm:w-[320px] md:w-[360px] lg:w-[380px] xl:w-[420px] aspect-[5/4] overflow-hidden rounded-xl cursor-pointer group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 flex-shrink-0"
                  onClick={() => openLightbox(0)}
                >
                  <Image
                    src={images[0].image}
                    alt={images[0].alt}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                    sizes="(max-width: 640px) 280px, (max-width: 768px) 320px, (max-width: 1024px) 360px, (max-width: 1280px) 380px, 420px"
                    priority
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
                  {/* 點擊提示 */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-white/90 rounded-full p-3 shadow-lg">
                      <svg className="w-6 h-6 text-[#2b354d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                      </svg>
                    </div>
                  </div>
                </CardMotion>

                {/* 右側三張小圖垂直排列 - 配合左側長方形圖片高度，使用一致的間距 */}
                <div className="flex flex-col justify-between gap-2 md:gap-3 lg:gap-4 w-20 sm:w-24 md:w-28 lg:w-36 xl:w-40 flex-shrink-0">
                  {images.slice(1).map((image, index) => (
                    <CardMotion
                      key={image.id}
                      index={index + 1}
                      variants={cardEntranceVariants}
                      className="relative aspect-[4/3] overflow-hidden rounded-lg cursor-pointer group hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
                      onClick={() => openLightbox(index + 1)}
                    >
                      <Image
                        src={image.image}
                        alt={image.alt}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-500"
                        sizes="(max-width: 640px) 80px, (max-width: 768px) 96px, (max-width: 1024px) 112px, (max-width: 1280px) 144px, 160px"
                      />
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
                      {/* 點擊提示 */}
                      <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="bg-white/90 rounded-full p-1.5 shadow-lg">
                          <svg className="w-3 h-3 md:w-4 md:h-4 text-[#2b354d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                          </svg>
                        </div>
                      </div>
                    </CardMotion>
                  ))}
                </div>
              </ScrollTriggerMotion>
            )}
          </div>
        </div>
      </section>

      {/* Lightbox */}
      {lightboxOpen && (
        <div className="fixed inset-0 bg-black/90 flex items-center justify-center" style={{ zIndex: 99999 }}>
          {/* 關閉按鈕 - Safari 兼容性修復 */}
          <button
            onClick={closeLightbox}
            className="text-white hover:text-gray-300 transition-colors"
            style={{
              position: 'fixed',
              top: '16px',
              right: '16px',
              zIndex: 999999,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              borderRadius: '50%',
              width: '48px',
              height: '48px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: 'none',
              cursor: 'pointer'
            }}
          >
            <X size={24} />
          </button>

          {/* 上一張按鈕 - Safari 兼容性修復 */}
          <button
            onClick={prevImage}
            className="text-white hover:text-gray-300 transition-all duration-200"
            style={{
              position: 'fixed',
              left: '16px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 999999,
              backgroundColor: 'rgba(0, 0, 0, 0.3)',
              borderRadius: '50%',
              width: '48px',
              height: '48px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: 'none',
              cursor: 'pointer'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
            }}
          >
            <ChevronLeft size={16} />
          </button>

          {/* 下一張按鈕 - Safari 兼容性修復 */}
          <button
            onClick={nextImage}
            className="text-white hover:text-gray-300 transition-all duration-200"
            style={{
              position: 'fixed',
              right: '16px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 999999,
              backgroundColor: 'rgba(0, 0, 0, 0.3)',
              borderRadius: '50%',
              width: '48px',
              height: '48px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: 'none',
              cursor: 'pointer'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
            }}
          >
            <ChevronRight size={16} />
          </button>

          {/* 主圖片 */}
          <div className="relative w-full h-full max-w-4xl max-h-[80vh] mx-4">
            <Image
              src={images[currentImageIndex].image}
              alt={images[currentImageIndex].alt}
              fill
              className="object-contain"
              priority
            />
          </div>

          {/* 圖片指示器 */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {images.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentImageIndex(index)}
                className={cn(
                  "w-3 h-3 rounded-full transition-colors",
                  index === currentImageIndex ? "bg-white" : "bg-white/50"
                )}
              />
            ))}
          </div>
        </div>
      )}
    </>
  );
};

export default DesignGallerySection;
